#!/usr/bin/env python3
"""
已完成
SQLite数据库测试脚本
验证数据库连接和基本功能
"""
import sys
import os
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import AsyncSessionLocal
from app.services.customer_service import CustomerServiceService
from app.tools.customer_service_tools import CustomerServiceTools, ProductSearchInput


async def test_database_connection():
    """测试数据库连接"""
    print("🔗 测试数据库连接...")
    try:
        async with AsyncSessionLocal() as db:
            # 简单查询测试
            from sqlalchemy import text
            result = await db.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("✅ 数据库连接成功")
                return True
            else:
                print("❌ 数据库连接失败")
                return False
    except Exception as e:
        print(f"❌ 数据库连接错误：{e}")
        return False


async def test_data_integrity():
    """测试数据完整性"""
    print("\n📊 测试数据完整性...")
    
    async with AsyncSessionLocal() as db:
        try:
            from sqlalchemy import text, select
            from app.models import User, Product, Order, FAQ, Promotion
            
            # 检查各表数据量
            tables_to_check = [
                (User, "用户"),
                (Product, "产品"),
                (Order, "订单"),
                (FAQ, "FAQ"),
                (Promotion, "促销活动")
            ]
            
            for model, name in tables_to_check:
                result = await db.execute(select(model))
                count = len(result.scalars().all())
                print(f"  📋 {name}表：{count} 条记录")
            
            print("✅ 数据完整性检查通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据完整性检查失败：{e}")
            return False


async def test_customer_service_functions():
    """测试客服功能"""
    print("\n🛠️ 测试客服功能...")
    
    async with AsyncSessionLocal() as db:
        service = CustomerServiceService(db)
        
        try:
            # 测试产品搜索
            print("  🔍 测试产品搜索...")
            products = await service.search_products(query="iPhone", limit=3)
            if products:
                print(f"    ✅ 找到 {len(products)} 个iPhone产品")
                print(f"    📱 第一个产品：{products[0]['name']} - ¥{products[0]['price']}")
            else:
                print("    ⚠️ 未找到iPhone产品")
            
            # 测试订单查询
            print("  📦 测试订单查询...")
            order = await service.get_order_by_number("YUE20231201001")
            if order:
                print(f"    ✅ 订单查询成功：{order['order_number']} - {order['status']}")
            else:
                print("    ⚠️ 订单查询失败")
            
            # 测试FAQ搜索
            print("  ❓ 测试FAQ搜索...")
            faqs = await service.search_faq("退货", limit=3)
            if faqs:
                print(f"    ✅ 找到 {len(faqs)} 个退货相关FAQ")
                print(f"    📝 第一个FAQ：{faqs[0]['question']}")
            else:
                print("    ⚠️ 未找到退货相关FAQ")
            
            # 测试促销活动
            print("  🎯 测试促销活动...")
            promotions = await service.get_active_promotions()
            if promotions:
                print(f"    ✅ 找到 {len(promotions)} 个活跃促销")
                print(f"    🎫 第一个促销：{promotions[0]['name']}")
            else:
                print("    ⚠️ 未找到活跃促销")
            
            print("✅ 客服功能测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 客服功能测试失败：{e}")
            import traceback
            traceback.print_exc()
            return False


async def test_tools_integration():
    """测试工具集成"""
    print("\n🔧 测试工具集成...")
    
    async with AsyncSessionLocal() as db:
        tools = CustomerServiceTools(db)
        
        try:
            # 测试产品搜索工具
            print("  🛠️ 测试产品搜索工具...")
            search_input = ProductSearchInput(query="MacBook", limit=2)
            result = await tools.search_products(search_input)
            
            # 解析JSON结果
            result_data = json.loads(result)
            if result_data.get("success"):
                products = result_data.get("products", [])
                print(f"    ✅ 工具搜索成功：找到 {len(products)} 个MacBook产品")
                if products:
                    print(f"    💻 第一个产品：{products[0]['name']}")
            else:
                print(f"    ❌ 工具搜索失败：{result_data.get('error')}")
            
            # 测试工具定义
            print("  📋 测试工具定义...")
            available_tools = tools.get_available_tools()
            print(f"    ✅ 系统提供 {len(available_tools)} 个可用工具")
            
            # 列出前5个工具
            tool_names = [tool['name'] for tool in available_tools[:5]]
            print(f"    🔧 工具示例：{', '.join(tool_names)}")
            
            print("✅ 工具集成测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 工具集成测试失败：{e}")
            import traceback
            traceback.print_exc()
            return False


async def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 YUE商城客服系统 - SQLite数据库测试")
    print("=" * 60)
    
    # 检查数据库文件
    db_file = "yue_platform.db"
    if not os.path.exists(db_file):
        print(f"❌ 数据库文件不存在：{db_file}")
        print("请先运行：python quick_start.py")
        return False
    
    print(f"📁 数据库文件：{db_file} ({os.path.getsize(db_file) / 1024:.1f} KB)")
    
    # 运行测试
    tests = [
        ("数据库连接", test_database_connection),
        ("数据完整性", test_data_integrity),
        ("客服功能", test_customer_service_functions),
        ("工具集成", test_tools_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if await test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常：{e}")
    
    # 测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        print("\n🚀 可以启动API服务：")
        print("uvicorn app.main:app --reload")
        print("\n📖 查看API文档：")
        print("http://localhost:8000/docs")
        return True
    else:
        print("⚠️ 部分测试失败，请检查系统配置。")
        return False


if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
