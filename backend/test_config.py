#!/usr/bin/env python3
"""
已完成
配置测试脚本
验证环境变量是否正确加载到配置中
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings


def test_config_loading():
    """测试配置加载"""
    print("🔧 测试配置加载...")
    print("=" * 50)
    
    # 测试基础配置
    print("📋 基础配置:")
    print(f"  项目名称: {settings.PROJECT_NAME}")
    print(f"  版本: {settings.VERSION}")
    print(f"  调试模式: {settings.DEBUG}")
    print(f"  主机: {settings.HOST}")
    print(f"  端口: {settings.PORT}")
    
    # 测试CORS配置
    print("\n🌐 CORS配置:")
    print(f"  允许的源: {settings.BACKEND_CORS_ORIGINS}")
    print(f"  源数量: {len(settings.BACKEND_CORS_ORIGINS)}")
    
    # 测试数据库配置
    print("\n🗄️ 数据库配置:")
    print(f"  数据库URL: {settings.DATABASE_URL}")
    
    # 测试OpenAI配置
    print("\n🤖 OpenAI配置:")
    print(f"  API密钥: {settings.OPENAI_API_KEY[:20]}..." if settings.OPENAI_API_KEY else "  API密钥: 未设置")
    print(f"  API基础URL: {settings.OPENAI_API_BASE}")
    print(f"  模型: {settings.LLM_MODEL}")
    
    # 测试JWT配置
    print("\n🔐 JWT配置:")
    print(f"  密钥: {settings.SECRET_KEY[:20]}..." if settings.SECRET_KEY else "  密钥: 未设置")
    print(f"  算法: {settings.ALGORITHM}")
    print(f"  过期时间: {settings.ACCESS_TOKEN_EXPIRE_MINUTES}分钟")
    
    # 测试客服配置
    print("\n👥 客服配置:")
    print(f"  客服名称: {settings.CUSTOMER_SERVICE_NAME}")
    print(f"  最大对话历史: {settings.MAX_CONVERSATION_HISTORY}")
    print(f"  人工转接关键词: {settings.HUMAN_HANDOVER_KEYWORDS}")
    
    # 测试日志配置
    print("\n📝 日志配置:")
    print(f"  日志级别: {settings.LOG_LEVEL}")
    print(f"  日志文件: {settings.LOG_FILE}")
    
    print("\n" + "=" * 50)
    print("✅ 配置加载测试完成")


def test_env_file_loading():
    """测试.env文件是否正确加载"""
    print("\n🔍 测试.env文件加载...")
    
    # 检查.env文件是否存在
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"✅ 找到.env文件: {env_file}")
        
        # 读取.env文件内容
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 .env文件包含 {len(lines)} 行配置")
        
        # 检查关键配置是否被正确加载
        key_configs = {
            "OPENAI_API_KEY": settings.OPENAI_API_KEY,
            "OPENAI_API_BASE": settings.OPENAI_API_BASE,
            "LLM_MODEL": settings.LLM_MODEL,
            "PROJECT_NAME": settings.PROJECT_NAME,
            "DATABASE_URL": settings.DATABASE_URL,
        }
        
        print("\n🔑 关键配置验证:")
        for key, value in key_configs.items():
            if value:
                if key == "OPENAI_API_KEY":
                    print(f"  ✅ {key}: {value[:20]}...")
                else:
                    print(f"  ✅ {key}: {value}")
            else:
                print(f"  ⚠️ {key}: 未设置或为空")
    else:
        print(f"⚠️ 未找到.env文件: {env_file}")


def test_llms_integration():
    """测试llms.py是否能正确使用配置"""
    print("\n🤖 测试LLM集成...")
    
    try:
        from app.core.llms import _setup_model_client, _setup_vllm_model_client
        
        print("✅ 成功导入LLM模块")
        
        # 测试模型客户端设置
        if settings.OPENAI_API_KEY:
            try:
                model_client = _setup_model_client()
                print("✅ OpenAI模型客户端设置成功")
                print(f"  模型: {settings.LLM_MODEL}")
                print(f"  API基础URL: {settings.OPENAI_API_BASE or '默认'}")
            except Exception as e:
                print(f"❌ OpenAI模型客户端设置失败: {e}")
        else:
            print("⚠️ 未设置OPENAI_API_KEY，跳过OpenAI客户端测试")
        
        # 测试VLLM客户端设置
        if settings.VLLM_API_KEY:
            try:
                vllm_client = _setup_vllm_model_client()
                print("✅ VLLM模型客户端设置成功")
            except Exception as e:
                print(f"❌ VLLM模型客户端设置失败: {e}")
        else:
            print("⚠️ 未设置VLLM_API_KEY，跳过VLLM客户端测试")
            
    except ImportError as e:
        print(f"❌ 导入LLM模块失败: {e}")
        print("  可能缺少autogen_ext依赖")


def main():
    """主函数"""
    print("🧪 YUE商城客服系统 - 配置测试")
    print("=" * 60)
    
    # 显示当前工作目录
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"🐍 Python路径: {sys.path[0]}")
    
    try:
        # 测试配置加载
        test_config_loading()
        
        # 测试.env文件加载
        test_env_file_loading()
        
        # 测试LLM集成
        test_llms_integration()
        
        print("\n" + "=" * 60)
        print("🎉 所有配置测试完成！")
        
        # 提供使用建议
        print("\n💡 使用建议:")
        if not settings.OPENAI_API_KEY:
            print("  - 请在.env文件中设置OPENAI_API_KEY")
        if settings.DEBUG:
            print("  - 生产环境请将DEBUG设置为false")
        if settings.SECRET_KEY == "your-secret-key-here":
            print("  - 请更改默认的SECRET_KEY")
            
    except Exception as e:
        print(f"\n❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
