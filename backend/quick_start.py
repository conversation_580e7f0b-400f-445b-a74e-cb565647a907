#!/usr/bin/env python3
"""
已完成
YUE商城客服系统快速启动脚本
使用SQLite数据库，包含丰富的测试数据
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.init_db import main as init_db_main


async def quick_start():
    """快速启动流程"""
    print("🚀 YUE商城智能客服系统 - 快速启动")
    print("=" * 50)
    
    print("📊 使用SQLite数据库")
    print("📁 数据库文件：./yue_platform.db")
    print()
    
    # 检查数据库文件是否存在
    db_file = "yue_platform.db"
    if os.path.exists(db_file):
        print(f"⚠️  发现已存在的数据库文件：{db_file}")
        choice = input("是否删除并重新创建？(y/N): ").lower().strip()
        if choice == 'y':
            os.remove(db_file)
            print("✅ 已删除旧数据库文件")
        else:
            print("📝 将使用现有数据库")
            return
    
    print("🔧 正在初始化数据库和创建测试数据...")
    try:
        await init_db_main()
        print("✅ 数据库初始化完成！")
        
        print("\n📋 测试数据概览：")
        print("👥 用户：4个测试用户（张三、李四、王五、赵六）")
        print("📦 产品：6个测试产品（iPhone、MacBook、小米手机、羽绒服、吸尘器等）")
        print("🛒 订单：3个测试订单（已完成、已发货、处理中）")
        print("🎫 优惠券：3张测试优惠券")
        print("🎯 促销活动：3个活跃促销")
        print("❓ FAQ：12个常见问题")
        print("📞 客服代理：2个在线客服")
        print("📝 投诉记录：2个测试投诉")
        
        print("\n🔧 启动API服务：")
        print("uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        
        print("\n📖 查看API文档：")
        print("http://localhost:8000/docs")
        
        print("\n🧪 运行功能演示：")
        print("python demo_customer_service.py")

        print("\n🔧 测试配置和LLM：")
        print("python test_config.py")
        print("python test_llm_config.py")

        print("\n🎯 测试场景示例：")
        print("1. 产品搜索：搜索'iPhone'或'MacBook'")
        print("2. 订单查询：订单号'YUE20231201001'")
        print("3. 物流追踪：运单号'SF1234567890'")
        print("4. 优惠券验证：代码'WELCOME200_USER2'")
        print("5. FAQ搜索：搜索'退货'或'支付'")
        print("6. 投诉查询：投诉号'CP20231201000001'")

        print("\n💡 配置说明：")
        print("- 系统使用.env文件进行配置")
        print("- 可以设置OpenAI API密钥启用AI功能")
        print("- 支持自定义API基础URL（如DeepSeek等）")

        print("\n" + "=" * 50)
        print("🎉 系统准备就绪！开始体验YUE商城智能客服系统吧！")
        
    except Exception as e:
        print(f"❌ 初始化失败：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(quick_start())
