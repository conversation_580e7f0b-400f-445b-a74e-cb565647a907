"""
客服工具封装
将客服服务方法封装成可供AI模型调用的工具
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.customer_service import CustomerServiceService
import json


class ProductSearchInput(BaseModel):
    """产品搜索输入"""
    query: Optional[str] = Field(None, description="搜索关键词")
    category_id: Optional[int] = Field(None, description="分类ID")
    min_price: Optional[float] = Field(None, description="最低价格")
    max_price: Optional[float] = Field(None, description="最高价格")
    in_stock: bool = Field(True, description="是否只显示有库存商品")
    limit: int = Field(10, description="返回结果数量限制")


class OrderQueryInput(BaseModel):
    """订单查询输入"""
    order_number: str = Field(..., description="订单号")


class CouponValidationInput(BaseModel):
    """优惠券验证输入"""
    code: str = Field(..., description="优惠券代码")
    user_id: Optional[int] = Field(None, description="用户ID")
    order_amount: Optional[float] = Field(None, description="订单金额")


class RefundRequestInput(BaseModel):
    """退款申请输入"""
    order_id: int = Field(..., description="订单ID")
    user_id: int = Field(..., description="用户ID")
    reason: str = Field(..., description="退款原因")
    amount: Optional[float] = Field(None, description="退款金额，不填则全额退款")


class ComplaintInput(BaseModel):
    """投诉输入"""
    user_id: int = Field(..., description="用户ID")
    category: str = Field(..., description="投诉分类")
    subject: str = Field(..., description="投诉主题")
    description: str = Field(..., description="投诉描述")
    order_id: Optional[int] = Field(None, description="关联订单ID")


class HumanAgentRequestInput(BaseModel):
    """人工客服请求输入"""
    user_id: int = Field(..., description="用户ID")
    conversation_id: str = Field(..., description="对话ID")
    reason: Optional[str] = Field(None, description="请求原因")


class CustomerServiceTools:
    """客服工具类"""
    
    def __init__(self, db: AsyncSession):
        self.service = CustomerServiceService(db)
    
    async def search_products(self, input_data: ProductSearchInput) -> str:
        """
        搜索产品信息
        
        Args:
            input_data: 产品搜索参数
            
        Returns:
            JSON格式的产品列表
        """
        try:
            products = await self.service.search_products(
                query=input_data.query,
                category_id=input_data.category_id,
                min_price=input_data.min_price,
                max_price=input_data.max_price,
                in_stock=input_data.in_stock,
                limit=input_data.limit
            )
            return json.dumps({"success": True, "products": products}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def get_product_details(self, product_id: int) -> str:
        """
        获取产品详细信息
        
        Args:
            product_id: 产品ID
            
        Returns:
            JSON格式的产品详情
        """
        try:
            product = await self.service.get_product_details(product_id)
            if product:
                return json.dumps({"success": True, "product": product}, ensure_ascii=False)
            else:
                return json.dumps({"success": False, "error": "产品不存在"}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def check_product_stock(self, product_id: int) -> str:
        """
        检查产品库存
        
        Args:
            product_id: 产品ID
            
        Returns:
            JSON格式的库存信息
        """
        try:
            stock_info = await self.service.check_product_stock(product_id)
            return json.dumps(stock_info, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def get_active_promotions(self) -> str:
        """
        获取当前活跃的促销活动
        
        Returns:
            JSON格式的促销活动列表
        """
        try:
            promotions = await self.service.get_active_promotions()
            return json.dumps({"success": True, "promotions": promotions}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def get_user_coupons(self, user_id: int, available_only: bool = True) -> str:
        """
        获取用户优惠券
        
        Args:
            user_id: 用户ID
            available_only: 是否只返回可用优惠券
            
        Returns:
            JSON格式的优惠券列表
        """
        try:
            coupons = await self.service.get_user_coupons(user_id, available_only)
            return json.dumps({"success": True, "coupons": coupons}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def validate_coupon(self, input_data: CouponValidationInput) -> str:
        """
        验证优惠券
        
        Args:
            input_data: 优惠券验证参数
            
        Returns:
            JSON格式的验证结果
        """
        try:
            result = await self.service.validate_coupon(
                code=input_data.code,
                user_id=input_data.user_id,
                order_amount=input_data.order_amount
            )
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"valid": False, "error": str(e)}, ensure_ascii=False)
    
    async def get_membership_benefits(self, user_id: int) -> str:
        """
        获取会员权益
        
        Args:
            user_id: 用户ID
            
        Returns:
            JSON格式的会员权益信息
        """
        try:
            benefits = await self.service.get_membership_benefits(user_id)
            return json.dumps(benefits, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"is_member": False, "error": str(e)}, ensure_ascii=False)
    
    async def get_order_by_number(self, input_data: OrderQueryInput) -> str:
        """
        根据订单号获取订单信息
        
        Args:
            input_data: 订单查询参数
            
        Returns:
            JSON格式的订单信息
        """
        try:
            order = await self.service.get_order_by_number(input_data.order_number)
            if order:
                return json.dumps({"success": True, "order": order}, ensure_ascii=False)
            else:
                return json.dumps({"success": False, "error": "订单不存在"}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def get_user_orders(self, user_id: int, limit: int = 10, offset: int = 0) -> str:
        """
        获取用户订单列表
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            JSON格式的订单列表
        """
        try:
            orders = await self.service.get_user_orders(user_id, limit, offset)
            return json.dumps({"success": True, "orders": orders}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)
    
    async def track_shipment(self, tracking_number: str) -> str:
        """
        追踪物流信息
        
        Args:
            tracking_number: 物流单号
            
        Returns:
            JSON格式的物流信息
        """
        try:
            shipment = await self.service.track_shipment(tracking_number)
            if shipment:
                return json.dumps({"success": True, "shipment": shipment}, ensure_ascii=False)
            else:
                return json.dumps({"success": False, "error": "物流信息不存在"}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def check_refund_eligibility(self, order_id: int, user_id: int = None) -> str:
        """
        检查退款资格

        Args:
            order_id: 订单ID
            user_id: 用户ID

        Returns:
            JSON格式的退款资格检查结果
        """
        try:
            result = await self.service.check_refund_eligibility(order_id, user_id)
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"eligible": False, "error": str(e)}, ensure_ascii=False)

    async def create_refund_request(self, input_data: RefundRequestInput) -> str:
        """
        创建退款申请

        Args:
            input_data: 退款申请参数

        Returns:
            JSON格式的退款申请结果
        """
        try:
            result = await self.service.create_refund_request(
                order_id=input_data.order_id,
                user_id=input_data.user_id,
                reason=input_data.reason,
                amount=input_data.amount
            )
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def get_refund_status(self, refund_number: str, user_id: int = None) -> str:
        """
        获取退款状态

        Args:
            refund_number: 退款单号
            user_id: 用户ID

        Returns:
            JSON格式的退款状态信息
        """
        try:
            refund = await self.service.get_refund_status(refund_number, user_id)
            if refund:
                return json.dumps({"success": True, "refund": refund}, ensure_ascii=False)
            else:
                return json.dumps({"success": False, "error": "退款记录不存在"}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def create_complaint(self, input_data: ComplaintInput) -> str:
        """
        创建投诉

        Args:
            input_data: 投诉参数

        Returns:
            JSON格式的投诉创建结果
        """
        try:
            result = await self.service.create_complaint(
                user_id=input_data.user_id,
                category=input_data.category,
                subject=input_data.subject,
                description=input_data.description,
                order_id=input_data.order_id
            )
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def get_complaint_status(self, complaint_number: str, user_id: int = None) -> str:
        """
        获取投诉状态

        Args:
            complaint_number: 投诉单号
            user_id: 用户ID

        Returns:
            JSON格式的投诉状态信息
        """
        try:
            complaint = await self.service.get_complaint_status(complaint_number, user_id)
            if complaint:
                return json.dumps({"success": True, "complaint": complaint}, ensure_ascii=False)
            else:
                return json.dumps({"success": False, "error": "投诉记录不存在"}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def search_faq(self, query: str, category_id: int = None, limit: int = 10) -> str:
        """
        搜索FAQ

        Args:
            query: 搜索关键词
            category_id: 分类ID
            limit: 返回数量限制

        Returns:
            JSON格式的FAQ列表
        """
        try:
            faqs = await self.service.search_faq(query, category_id, limit)
            return json.dumps({"success": True, "faqs": faqs}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def request_human_agent(self, input_data: HumanAgentRequestInput) -> str:
        """
        请求人工客服

        Args:
            input_data: 人工客服请求参数

        Returns:
            JSON格式的请求结果
        """
        try:
            result = await self.service.request_human_agent(
                user_id=input_data.user_id,
                conversation_id=input_data.conversation_id,
                reason=input_data.reason
            )
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"success": False, "error": str(e)}, ensure_ascii=False)

    async def check_human_queue_status(self, conversation_id: str, user_id: int) -> str:
        """
        检查人工客服队列状态

        Args:
            conversation_id: 对话ID
            user_id: 用户ID

        Returns:
            JSON格式的队列状态信息
        """
        try:
            result = await self.service.check_human_queue_status(conversation_id, user_id)
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """
        获取可用工具列表（供AI模型调用）

        Returns:
            工具定义列表
        """
        return [
            {
                "name": "search_products",
                "description": "搜索产品信息，支持关键词、分类、价格范围等条件",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索关键词"},
                        "category_id": {"type": "integer", "description": "分类ID"},
                        "min_price": {"type": "number", "description": "最低价格"},
                        "max_price": {"type": "number", "description": "最高价格"},
                        "in_stock": {"type": "boolean", "description": "是否只显示有库存商品", "default": True},
                        "limit": {"type": "integer", "description": "返回结果数量限制", "default": 10}
                    }
                }
            },
            {
                "name": "get_product_details",
                "description": "获取产品详细信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "product_id": {"type": "integer", "description": "产品ID"}
                    },
                    "required": ["product_id"]
                }
            },
            {
                "name": "check_product_stock",
                "description": "检查产品库存状态",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "product_id": {"type": "integer", "description": "产品ID"}
                    },
                    "required": ["product_id"]
                }
            },
            {
                "name": "get_active_promotions",
                "description": "获取当前活跃的促销活动",
                "parameters": {"type": "object", "properties": {}}
            },
            {
                "name": "get_user_coupons",
                "description": "获取用户优惠券",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "integer", "description": "用户ID"},
                        "available_only": {"type": "boolean", "description": "是否只返回可用优惠券", "default": True}
                    },
                    "required": ["user_id"]
                }
            },
            {
                "name": "validate_coupon",
                "description": "验证优惠券有效性",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "优惠券代码"},
                        "user_id": {"type": "integer", "description": "用户ID"},
                        "order_amount": {"type": "number", "description": "订单金额"}
                    },
                    "required": ["code"]
                }
            },
            {
                "name": "get_order_by_number",
                "description": "根据订单号获取订单信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_number": {"type": "string", "description": "订单号"}
                    },
                    "required": ["order_number"]
                }
            },
            {
                "name": "track_shipment",
                "description": "追踪物流信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "tracking_number": {"type": "string", "description": "物流单号"}
                    },
                    "required": ["tracking_number"]
                }
            },
            {
                "name": "check_refund_eligibility",
                "description": "检查订单退款资格",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_id": {"type": "integer", "description": "订单ID"},
                        "user_id": {"type": "integer", "description": "用户ID"}
                    },
                    "required": ["order_id"]
                }
            },
            {
                "name": "create_refund_request",
                "description": "创建退款申请",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_id": {"type": "integer", "description": "订单ID"},
                        "user_id": {"type": "integer", "description": "用户ID"},
                        "reason": {"type": "string", "description": "退款原因"},
                        "amount": {"type": "number", "description": "退款金额，不填则全额退款"}
                    },
                    "required": ["order_id", "user_id", "reason"]
                }
            },
            {
                "name": "search_faq",
                "description": "搜索常见问题",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索关键词"},
                        "category_id": {"type": "integer", "description": "分类ID"},
                        "limit": {"type": "integer", "description": "返回数量限制", "default": 10}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "request_human_agent",
                "description": "请求人工客服",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "integer", "description": "用户ID"},
                        "conversation_id": {"type": "string", "description": "对话ID"},
                        "reason": {"type": "string", "description": "请求原因"}
                    },
                    "required": ["user_id", "conversation_id"]
                }
            }
        ]
