#!/usr/bin/env python3
"""
已完成
YUE商城客服系统演示脚本
展示六大客服场景的功能
"""
import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import AsyncSessionLocal
from app.tools.customer_service_tools import (
    CustomerServiceTools,
    ProductSearchInput,
    CouponValidationInput,
    RefundRequestInput,
    ComplaintInput,
    HumanAgentRequestInput
)


class CustomerServiceDemo:
    """客服系统演示类"""
    
    def __init__(self):
        self.tools = None
    
    async def setup(self):
        """初始化"""
        self.db = AsyncSessionLocal()
        self.tools = CustomerServiceTools(self.db)
    
    async def cleanup(self):
        """清理资源"""
        if self.db:
            await self.db.close()
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def print_result(self, result: str, title: str = "结果"):
        """打印结果"""
        try:
            data = json.loads(result)
            print(f"\n{title}:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
        except:
            print(f"\n{title}: {result}")
    
    async def demo_scenario_1(self):
        """场景1: 售前咨询 - 产品信息"""
        self.print_section("场景1: 售前咨询 - 产品信息")
        
        print("\n1.1 搜索iPhone产品")
        search_input = ProductSearchInput(
            query="iPhone",
            min_price=5000,
            max_price=10000,
            limit=3
        )
        result = await self.tools.search_products(search_input)
        self.print_result(result, "搜索结果")
        
        print("\n1.2 查看产品详情")
        result = await self.tools.get_product_details(1)
        self.print_result(result, "产品详情")
        
        print("\n1.3 检查库存状态")
        result = await self.tools.check_product_stock(1)
        self.print_result(result, "库存信息")
    
    async def demo_scenario_2(self):
        """场景2: 售前咨询 - 活动与优惠"""
        self.print_section("场景2: 售前咨询 - 活动与优惠")
        
        print("\n2.1 查看当前促销活动")
        result = await self.tools.get_active_promotions()
        self.print_result(result, "促销活动")
        
        print("\n2.2 验证优惠券")
        coupon_input = CouponValidationInput(
            code="DOUBLE11",
            user_id=1,
            order_amount=1000.0
        )
        result = await self.tools.validate_coupon(coupon_input)
        self.print_result(result, "优惠券验证")
        
        print("\n2.3 查看会员权益")
        result = await self.tools.get_membership_benefits(1)
        self.print_result(result, "会员权益")
    
    async def demo_scenario_3(self):
        """场景3: 订单追踪"""
        self.print_section("场景3: 订单追踪")
        
        print("\n3.1 查询订单信息")
        from app.tools.customer_service_tools import OrderQueryInput
        order_input = OrderQueryInput(order_number="ORD20231201001")
        result = await self.tools.get_order_by_number(order_input)
        self.print_result(result, "订单信息")
        
        print("\n3.2 查看用户订单列表")
        result = await self.tools.get_user_orders(1, limit=5)
        self.print_result(result, "订单列表")
        
        print("\n3.3 追踪物流信息")
        result = await self.tools.track_shipment("SF1234567890")
        self.print_result(result, "物流信息")
    
    async def demo_scenario_4(self):
        """场景4: 售后服务 - 退换货申请"""
        self.print_section("场景4: 售后服务 - 退换货申请")
        
        print("\n4.1 检查退款资格")
        result = await self.tools.check_refund_eligibility(1, 1)
        self.print_result(result, "退款资格")
        
        print("\n4.2 创建退款申请")
        refund_input = RefundRequestInput(
            order_id=1,
            user_id=1,
            reason="商品质量问题，申请退款",
            amount=999.0
        )
        result = await self.tools.create_refund_request(refund_input)
        self.print_result(result, "退款申请")
        
        print("\n4.3 查询退款状态")
        result = await self.tools.get_refund_status("RF20231201000001", 1)
        self.print_result(result, "退款状态")
    
    async def demo_scenario_5(self):
        """场景5: 投诉与建议"""
        self.print_section("场景5: 投诉与建议")
        
        print("\n5.1 创建投诉")
        complaint_input = ComplaintInput(
            user_id=1,
            category="服务质量",
            subject="客服响应太慢",
            description="等待客服回复超过30分钟，希望改进服务效率",
            order_id=1
        )
        result = await self.tools.create_complaint(complaint_input)
        self.print_result(result, "投诉创建")
        
        print("\n5.2 搜索常见问题")
        result = await self.tools.search_faq("退货政策", limit=3)
        self.print_result(result, "FAQ搜索")
        
        print("\n5.3 查询投诉状态")
        result = await self.tools.get_complaint_status("CP20231201000001", 1)
        self.print_result(result, "投诉状态")
    
    async def demo_scenario_6(self):
        """场景6: 请求人工服务"""
        self.print_section("场景6: 请求人工服务")
        
        print("\n6.1 请求人工客服")
        human_request = HumanAgentRequestInput(
            user_id=1,
            conversation_id=f"demo_conv_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            reason="需要详细的产品技术咨询"
        )
        result = await self.tools.request_human_agent(human_request)
        self.print_result(result, "人工客服请求")
        
        print("\n6.2 检查队列状态")
        result = await self.tools.check_human_queue_status(
            human_request.conversation_id, 
            human_request.user_id
        )
        self.print_result(result, "队列状态")
    
    async def demo_tools_overview(self):
        """展示工具概览"""
        self.print_section("工具概览")
        
        print("\n可用工具列表:")
        available_tools = self.tools.get_available_tools()
        
        for i, tool in enumerate(available_tools, 1):
            print(f"\n{i}. {tool['name']}")
            print(f"   描述: {tool['description']}")
            required_params = tool['parameters'].get('required', [])
            if required_params:
                print(f"   必需参数: {', '.join(required_params)}")
    
    async def run_demo(self):
        """运行完整演示"""
        print("🎉 欢迎使用YUE商城智能客服系统演示")
        print("本演示将展示六大核心客服场景的功能")
        
        try:
            await self.setup()
            
            # 展示工具概览
            await self.demo_tools_overview()
            
            # 演示六大场景
            await self.demo_scenario_1()
            await self.demo_scenario_2()
            await self.demo_scenario_3()
            await self.demo_scenario_4()
            await self.demo_scenario_5()
            await self.demo_scenario_6()
            
            self.print_section("演示完成")
            print("\n✅ 所有场景演示完成！")
            print("\n📝 说明:")
            print("- 部分功能可能因为测试数据不完整而返回空结果")
            print("- 实际使用时需要先运行 init_database.py 初始化数据")
            print("- 可以通过 API 接口或直接调用工具方法使用这些功能")
            print("\n🚀 启动API服务: uvicorn app.main:app --reload")
            print("📖 查看API文档: http://localhost:8000/docs")
            
        except Exception as e:
            print(f"\n❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    demo = CustomerServiceDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
