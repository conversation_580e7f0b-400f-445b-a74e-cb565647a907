#!/usr/bin/env python3
"""
已完成
客服系统测试脚本
"""
import sys
import os
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import AsyncSessionLocal
from app.services.customer_service import CustomerServiceService
from app.tools.customer_service_tools import CustomerServiceTools, ProductSearchInput


async def test_product_search():
    """测试产品搜索功能"""
    print("=== 测试产品搜索功能 ===")
    
    async with AsyncSessionLocal() as db:
        service = CustomerServiceService(db)
        
        # 搜索iPhone产品
        products = await service.search_products(query="iPhone", limit=5)
        print(f"搜索到 {len(products)} 个iPhone产品")
        
        if products:
            product = products[0]
            print(f"产品名称: {product['name']}")
            print(f"价格: ¥{product['price']}")
            print(f"库存: {product['stock_quantity']}")
            
            # 获取产品详情
            details = await service.get_product_details(product['id'])
            if details:
                print(f"产品详情获取成功，规格: {details.get('specifications', {})}")


async def test_promotions():
    """测试促销活动功能"""
    print("\n=== 测试促销活动功能 ===")
    
    async with AsyncSessionLocal() as db:
        service = CustomerServiceService(db)
        
        # 获取活跃促销
        promotions = await service.get_active_promotions()
        print(f"当前有 {len(promotions)} 个活跃促销活动")
        
        if promotions:
            promo = promotions[0]
            print(f"活动名称: {promo['name']}")
            print(f"折扣类型: {promo['discount_type']}")
            print(f"折扣值: {promo['discount_value']}")


async def test_faq_search():
    """测试FAQ搜索功能"""
    print("\n=== 测试FAQ搜索功能 ===")
    
    async with AsyncSessionLocal() as db:
        service = CustomerServiceService(db)
        
        # 搜索退货相关FAQ
        faqs = await service.search_faq("退货", limit=3)
        print(f"搜索到 {len(faqs)} 个相关FAQ")
        
        for faq in faqs:
            print(f"问题: {faq['question']}")
            print(f"答案: {faq['answer'][:50]}...")


async def test_tools_integration():
    """测试工具集成功能"""
    print("\n=== 测试工具集成功能 ===")
    
    async with AsyncSessionLocal() as db:
        tools = CustomerServiceTools(db)
        
        # 测试产品搜索工具
        search_input = ProductSearchInput(query="MacBook", limit=2)
        result = await tools.search_products(search_input)
        
        # 解析JSON结果
        result_data = json.loads(result)
        if result_data.get("success"):
            products = result_data.get("products", [])
            print(f"工具搜索到 {len(products)} 个MacBook产品")
            
            if products:
                product = products[0]
                print(f"产品: {product['name']} - ¥{product['price']}")
        
        # 获取可用工具列表
        available_tools = tools.get_available_tools()
        print(f"系统提供 {len(available_tools)} 个可用工具")
        
        tool_names = [tool['name'] for tool in available_tools]
        print(f"工具列表: {', '.join(tool_names[:5])}...")


async def test_order_simulation():
    """测试订单相关功能（模拟）"""
    print("\n=== 测试订单功能（模拟） ===")
    
    async with AsyncSessionLocal() as db:
        service = CustomerServiceService(db)
        
        # 尝试查询一个不存在的订单（演示错误处理）
        order = await service.get_order_by_number("TEST_ORDER_001")
        if order is None:
            print("订单查询测试通过：正确处理了不存在的订单")
        
        # 检查退款资格（模拟）
        eligibility = await service.check_refund_eligibility(999, 1)
        if not eligibility.get("eligible"):
            print(f"退款资格检查测试通过：{eligibility.get('reason')}")


async def test_human_agent_request():
    """测试人工客服请求功能"""
    print("\n=== 测试人工客服请求功能 ===")
    
    async with AsyncSessionLocal() as db:
        service = CustomerServiceService(db)
        
        # 请求人工客服
        result = await service.request_human_agent(
            user_id=1,
            conversation_id="test_conv_001",
            reason="测试人工客服功能"
        )
        
        if result.get("success"):
            status = result.get("status")
            message = result.get("message")
            print(f"人工客服请求状态: {status}")
            print(f"系统消息: {message}")
        else:
            print(f"人工客服请求失败: {result.get('error')}")


async def main():
    """主测试函数"""
    print("开始测试YUE商城客服系统...")
    print("=" * 50)
    
    try:
        await test_product_search()
        await test_promotions()
        await test_faq_search()
        await test_tools_integration()
        await test_order_simulation()
        await test_human_agent_request()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！系统运行正常。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
